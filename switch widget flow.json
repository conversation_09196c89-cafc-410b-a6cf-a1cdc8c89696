[{"id": "2ad5052cfb12f2ae", "type": "tab", "label": "[4] MATCH-1-CAM1ISO-eu-cont-az2", "disabled": false, "info": "// nrlint align-to-grid:off", "env": []}, {"id": "10f9f18d8ea1ce64", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[007] - Source Switching Logic", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["c0d3b48d6d9a2436", "20a3b4d3fc826bd4", "0e883d92f38845bb", "35076bd1a5387c28", "393a040e266d4fc5", "8e9b9e3ae9b7062d"], "x": 1034, "y": 539, "w": 352, "h": 202}, {"id": "b93f0ca3c2683877", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[006] - User Interface", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["be86550136a3cadc", "cfa84292d3b4a231", "1eb86c025d873fec"], "x": 704, "y": 719, "w": 152, "h": 142}, {"id": "fbc22c4046eb250b", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[005] - State Management & Mapping", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["e722ce8d804812b0", "a0d7a732a3594f97"], "x": 664, "y": 559, "w": 236, "h": 82}, {"id": "02851485590b35b6", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[004] - Response Handling & Join", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["d8dbc957d90c14b5", "c1b883b40cd16596", "aaeb4c4abec81b4b", "559b3c8ba7e65d63", "c7f87e99c8c3662c", "3a2dace9cca58a66", "a83bf48993197dad", "df19abe2768339d0", "40789a6216d5beb5"], "x": 44, "y": 539, "w": 482, "h": 282}, {"id": "94f9dcc4badd6e17", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[003] - API Processing & HTTP Requests", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["675b74351fa25e40", "cfd9385a6ae54342", "b5d20460914acbb2", "c3d1ac8be15bda1d", "e152a5207e38aaea"], "x": 914, "y": 239, "w": 402, "h": 142}, {"id": "da0748b76365bcee", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[002] - Dynamic API Orchestration", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["cd16e84b5eb0e803", "32f7cd8b9b5638c3", "7b1769775f8f615a", "c2f63a97f5008000", "49f88af475683595"], "x": 434, "y": 184, "w": 432, "h": 257}, {"id": "6e6b6d145d9c6d01", "type": "group", "z": "2ad5052cfb12f2ae", "name": "[001] - Configuration & Timer", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["aa5ffe42cc414351", "b40fb08a4133735f", "e4484e4f9a5621e0"], "x": 64, "y": 179, "w": 342, "h": 142}, {"id": "c0d3b48d6d9a2436", "type": "function", "z": "2ad5052cfb12f2ae", "g": "10f9f18d8ea1ce64", "name": "Format Switch API Request", "func": "// Get the new source from the previous node's output\nconst newSource = msg.payload;\nconst config = flow.get('edgeConfig');\n\nif (!config || !newSource) {\n    return null;\n}\n\n// Log the request intent\nnode.log(`[REQ] API call to switch source to: ${newSource}`);\n\n// Find the source configuration\nlet targetSource = null;\nif (newSource === 'main') {\n    targetSource = config.sources.find(s => s.type === 'Main');\n} else if (newSource === 'backup') {\n    targetSource = config.sources.find(s => s.type === 'Backup');\n} else {\n    targetSource = config.sources.find(s => s.type.toLowerCase() === newSource.toLowerCase());\n}\n\nif (!targetSource) {\n    node.error(`No source found for type: ${newSource}`);\n    return null;\n}\n\nconst baseUrl = global.get('SECRETS.TECHEX_TXCORE_URL_PROD');\nconst apiKey = global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD');\n\n// Create the message for the API call to activate the selected source\nmsg.url = `${baseUrl}/api/mwedge/${config.edgeId}/source/${targetSource.sourceId}`;\nmsg.method = 'PUT';\nmsg.headers = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'Authorization': `Bearer ${apiKey}`\n};\nmsg.payload = { active: true };\nmsg.currentSource = newSource;\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1075, "y": 640, "wires": [["20a3b4d3fc826bd4"]], "l": false}, {"id": "20a3b4d3fc826bd4", "type": "http request", "z": "2ad5052cfb12f2ae", "g": "10f9f18d8ea1ce64", "name": "Switch API Call", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1145, "y": 640, "wires": [["0e883d92f38845bb"]], "l": false}, {"id": "0e883d92f38845bb", "type": "function", "z": "2ad5052cfb12f2ae", "g": "10f9f18d8ea1ce64", "name": "Handle Switch Response", "func": "const statusCode = msg.statusCode || 0;\nconst time = new Date().toISOString();\n\n// Error path: If the API call fails\nif (statusCode !== 200) {\n    const errorPayload = {\n        success: false,\n        action: `Switch to ${msg.currentSource}`,\n        error: `API call failed with status ${statusCode}`,\n        details: `Response: ${JSON.stringify(msg.payload)}`,\n        timestamp: time\n    };\n    \n    msg.payload = errorPayload;\n    msg.topic = \"error\";\n    \n    node.error(`[ERROR] Switch API returned ${statusCode}`, msg);\n    return [null, msg, msg]; // Send to debug (null), error debug, and UI\n}\n\n// Success path: If the API call is successful\nconst newSource = msg.currentSource;\nconst successPayload = {\n    success: true,\n    action: `Switched to ${newSource}`,\n    message: `Source successfully switched to '${newSource}'`,\n    timestamp: time\n};\n\nmsg.payload = successPayload;\nmsg.topic = \"success\";\n\nnode.log(`[SUCCESS] Source switched to '${newSource}'`);\nreturn [msg, null, msg]; // Send success to debug, error (null), and UI", "outputs": 3, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1225, "y": 640, "wires": [["35076bd1a5387c28"], ["393a040e266d4fc5"], ["8e9b9e3ae9b7062d"]], "l": false}, {"id": "35076bd1a5387c28", "type": "debug", "z": "2ad5052cfb12f2ae", "g": "10f9f18d8ea1ce64", "name": "Debug: Switch Success", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "payload", "statusType": "auto", "x": 1225, "y": 580, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "393a040e266d4fc5", "type": "debug", "z": "2ad5052cfb12f2ae", "g": "10f9f18d8ea1ce64", "name": "Debug: <PERSON><PERSON> E<PERSON>r", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "payload", "statusType": "auto", "x": 1325, "y": 640, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "8e9b9e3ae9b7062d", "type": "link out", "z": "2ad5052cfb12f2ae", "g": "10f9f18d8ea1ce64", "name": "link out 14", "mode": "link", "links": ["1eb86c025d873fec", "e4484e4f9a5621e0"], "x": 1225, "y": 700, "wires": []}, {"id": "be86550136a3cadc", "type": "ui_template", "z": "2ad5052cfb12f2ae", "g": "b93f0ca3c2683877", "group": "abdca6d8da3c3f58", "name": "Source Switch Widget", "order": 1, "width": "7", "height": "8", "format": "<style>\n    body {\n        background-color: #1e1e1e;\n        font-family: 'Fira Code', monospace;\n        color: #ccc;\n        margin: 0;\n        padding: 10px;\n        font-size: 0.8rem;\n        overflow: hidden !important;\n        width: 100%;\n        height: 100%;\n    }\n\n    .card {\n        background-color: #252526;\n        border-radius: 6px;\n        padding: 5px;\n        width: 100%;\n        height: 100%;\n        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);\n        box-sizing: border-box;\n        transform: scale(0.75);\n        transform-origin: top left; \n        overflow: hidden !important;\n    }\n\n    .card-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        border-bottom: 1px solid #333;\n        padding-bottom: 5px;\n        margin-bottom: 15px;\n    }\n\n    .title {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        font-weight: bold;\n        color: #ccc;\n    }\n\n    .title svg {\n        cursor: pointer;\n        width: 24px;\n        height: 24px;\n        fill: silver;\n        transition: fill 0.2s ease-in-out;\n    }\n\n    .status-time {\n        font-size: 0.85rem;\n        color: #66c2a5;\n    }\n\n    .label {\n        color: #888;\n        margin-bottom: 2px;\n        font-size: 0.75rem; \n    }\n\n    .value {\n        color: #66c2a5;\n        margin-bottom: 10px;\n        font-size: 0.8rem;\n    }\n\n    .value-main {\n        color: #ccc;\n        margin-bottom: 5px;\n        word-wrap: break-word;\n        line-height: 1.2;\n        font-size: 0.85rem;\n    }\n\n    .source-name {\n        color: #66c2a5;\n        font-size: 0.75rem;\n        margin-bottom: 10px;\n        word-wrap: break-word;\n        line-height: 1.2;\n    }\n\n    .switch-btn {\n        background-color: #3a8dde;\n        color: white;\n        padding: 4px 10px;\n        border: none;\n        border-radius: 5px;\n        cursor: pointer;\n        font-family: 'Fira Code', monospace;\n        font-size: 0.75rem;\n        transition: background-color 0.2s;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 5px;\n    }\n\n    .switch-btn:hover:not(:disabled) {\n        background-color: #2d77c7;\n    }\n\n    .switch-btn:disabled {\n        background-color: #2a2a2a;\n        color: #666;\n        cursor: not-allowed;\n    }\n\n    .switch-btn.loading {\n        background-color: #f59e0b;\n        cursor: not-allowed;\n    }\n\n    .flex-row {\n        display: flex;\n        justify-content: space-between;\n        align-items: flex-start;\n        margin-bottom: 10px;\n        gap: 10px;\n    }\n\n    .source-info {\n        flex: 1;\n        min-width: 0;\n    }\n\n    .online-indicator {\n        display: inline-block;\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 5px;\n    }\n\n    .online-indicator.online {\n        background-color: #66c2a5;\n    }\n\n    .online-indicator.offline {\n        background-color: #ef4444;\n    }\n\n    .spinner {\n        width: 12px;\n        height: 12px;\n        border: 2px solid #ffffff33;\n        border-top: 2px solid #ffffff;\n        border-radius: 50%;\n        animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n        0% {\n            transform: rotate(0deg);\n        }\n\n        100% {\n            transform: rotate(360deg);\n        }\n    }\n\n    .source-buttons {\n        display: flex;\n        gap: 5px;\n        margin-bottom: 10px;\n        flex-wrap: wrap;\n    }\n\n    .source-btn {\n        background-color: #3a8dde;\n        color: white;\n        padding: 6px 14px;\n        border: none;\n        border-radius: 5px;\n        cursor: pointer;\n        font-family: 'Fira Code', monospace;\n        transition: background-color 0.2s;\n        flex: 1;\n        min-width: 80px;\n    }\n\n    .source-btn:hover:not(:disabled) {\n        background-color: #2d77c7;\n    }\n\n    .source-btn:disabled {\n        background-color: #2a2a2a;\n        color: #666;\n        cursor: not-allowed;\n    }\n\n    .source-btn.active {\n        background-color: #66c2a5;\n        color: #1e1e1e;\n    }\n\n    .source-btn.selected {\n        background-color: #f59e0b;\n        color: #1e1e1e;\n    }\n\n    select {\n        width: 100%;\n        padding: 4px;\n        background-color: #1e1e1e;\n        color: #ccc;\n        border: 1px solid #444;\n        border-radius: 5px;\n        font-family: 'Fira Code', monospace;\n        font-size: 0.75rem;\n    }\n\n    select:disabled {\n        background-color: #2a2a2a;\n        color: #666;\n        border-color: #333;\n        cursor: not-allowed;\n    }\n\n    .last-updated {\n        font-size: 0.75rem;\n        color: #888;\n        margin-left: 5px;\n    }\n\n    .edge-info-link {\n    margin: 0 4px;\n    text-decoration: none;\n    font-size: 1em;\n    cursor: pointer;\n    }\n\n    .edge-info-link:hover {\n    color: #0078d4; /* optional hover color */\n    }\n</style>\n\n<!-- Removed Source Selection Section -->\n<div class=\"card\">\n    <div class=\"card-header\">\n        <div class=\"title\">\n            <svg id=\"lockIcon\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 \n                2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z\" />\n            </svg>\n            <span id=\"edgeName\">Source Switch Control</span>\n        </div>\n        <div class=\"status-time\">\n            <a id=\"edge-config-link\" class=\"edge-info-link\" target=\"_blank\" title=\"Open Edge Config\">ℹ️</a>\n            <span class=\"online-indicator\" id=\"onlineIndicator\"></span>\n            <span id=\"lastUpdated\">--</span>\n        </div>\n    </div>\n\n    <div>\n        <div class=\"label\">Active Stream:</div>\n        <div class=\"value\" id=\"activeStream\">--</div>\n\n        <div class=\"label\">Current Active Source:</div>\n        <div class=\"flex-row\">\n            <div class=\"source-info\">\n                <div class=\"value-main\" id=\"currentSource\">--</div>\n                <div class=\"source-name\" id=\"sourceName\">--</div>\n            </div>\n            <button class=\"switch-btn\" id=\"switchBtn\" disabled>\n                <span id=\"switchBtnText\">Switch Now</span>\n            </button>\n        </div>\n\n        <div class=\"label\">Source Dropdown:</div>\n        <select id=\"sourceSelect\" disabled>\n            <option>Loading...</option>\n        </select>\n\n        <div class=\"label\" style=\"margin-top: 10px;\">Output Stream:</div>\n        <div class=\"value\" id=\"outputStream\">--</div>\n    </div>\n</div>\n\n<script>\n    (function(scope) {\n    const lockIcon = document.getElementById('lockIcon');\n    const sourceSelect = document.getElementById('sourceSelect');\n    const edgeName = document.getElementById('edgeName');\n    const activeStream = document.getElementById('activeStream');\n    const currentSource = document.getElementById('currentSource');\n    const sourceName = document.getElementById('sourceName');\n    const outputStream = document.getElementById('outputStream');\n    const onlineIndicator = document.getElementById('onlineIndicator');\n    const lastUpdated = document.getElementById('lastUpdated');\n    const switchBtn = document.getElementById('switchBtn');\n    const switchBtnText = document.getElementById('switchBtnText');\n\n    let locked = true;\n    let currentConfig = null;\n    let currentState = null;\n    let availableSources = [];\n    let selectedSourceType = null; // Pre-selected source for Switch Now\n    let isLoading = false;\n    let currentActiveSourceType = null; // track currently active source\n\n    const lockSVG = `<path d=\"M12 17a2 2 0 100-4 2 2 0 000 4zm6-7h-1V7a5 5 0 00-10 0v3H6a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2v-8a2 2 0 00-2-2zm-8-3a3 3 0 016 0v3H10V7z\"/>`;\n    const unlockSVG = `<path d=\"M17 8V7a5 5 0 00-9.9-1h2.1a3 3 0 015.8 1v1H8a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-8a2 2 0 00-2-2h-1z\"/>`;\n\n    function toggleLock() {\n        locked = !locked;\n        lockIcon.innerHTML = locked ? lockSVG : unlockSVG;\n        updateButtonStates();\n    }\n\n    function updateButtonStates() {\n        sourceSelect.disabled = locked || isLoading;\n        // Switch Now enabled only if a source is selected AND it's different from current active\n        switchBtn.disabled = locked || isLoading || !selectedSourceType || selectedSourceType === currentActiveSourceType;\n    }\n\n    function setLoadingState(loading) {\n        isLoading = loading;\n        if (loading) {\n            switchBtn.classList.add('loading');\n            switchBtn.disabled = true;\n            switchBtnText.innerHTML = '<div class=\"spinner\"></div> Switching...';\n        } else {\n            switchBtn.classList.remove('loading');\n            switchBtnText.textContent = 'Switch Now';\n            updateButtonStates();\n        }\n    }\n\n    function selectSource(sourceType) {\n        selectedSourceType = sourceType;\n        updateButtonStates();\n    }\n\nfunction populateSourceDropdown(sources) {\navailableSources = sources || [];\nsourceSelect.innerHTML = '';\nconst defaultOption = document.createElement('option');\ndefaultOption.value = '';\ndefaultOption.textContent = 'Select source...';\nsourceSelect.appendChild(defaultOption);\n\navailableSources.forEach(source => {\nconst option = document.createElement('option');\noption.value = source.type.toLowerCase();\noption.textContent = source.type;\nsourceSelect.appendChild(option);\n});\n\n// Pre-select the current active source if exists\nif (currentActiveSourceType) {\nsourceSelect.value = currentActiveSourceType.toLowerCase();\nselectedSourceType = currentActiveSourceType;\n}\n\nupdateButtonStates();\n}\n\nfunction updateActiveSource(activeSourceType) {\ncurrentActiveSourceType = activeSourceType;\nconst activeSource = availableSources.find(s => s.type.toLowerCase() === activeSourceType);\nif (activeSource) {\ncurrentSource.textContent = activeSource.type || '--';\nsourceName.textContent = activeSource.name || activeSource.sourceName || '--';\n} else {\ncurrentSource.textContent = '--';\nsourceName.textContent = '--';\n}\n\n// Only update selectedSourceType if it’s not set yet\nif (!selectedSourceType || selectedSourceType !== currentActiveSourceType) {\nselectedSourceType = currentActiveSourceType;\nsourceSelect.value = currentActiveSourceType.toLowerCase();\n}\n\nupdateButtonStates();\n}\n\n    function switchToSelectedSource() {\n        if (locked || isLoading || !selectedSourceType) return;\n\n        // Only switch if the selected source is different from current\n        if (selectedSourceType === currentActiveSourceType) return;\n\n        setLoadingState(true);\n\n        scope.send({\n            currentSource: selectedSourceType,\n            action: 'switch',\n            switchType: 'preselected',\n            timestamp: new Date().toISOString()\n        });\n    }\n\n    function updateLastUpdated() {\n        const now = new Date();\n        lastUpdated.textContent = `${now.toLocaleTimeString()}`;\n    }\n\n    lockIcon.addEventListener('click', toggleLock);\n    lockIcon.innerHTML = lockSVG;\n    switchBtn.addEventListener('click', switchToSelectedSource);\n\n    // Dropdown selection only sets the value, doesn't trigger any message\n    sourceSelect.addEventListener('change', (e) => {\n        if (!locked && !isLoading && e.target.value) {\n            selectSource(e.target.value);\n        }\n    });\n\n    let linkSet = false;\n\n    // Watch for incoming messages from Node-RED\n    scope.$watch('msg', function(msg) {\n        if (!msg || !msg.payload) return;\n\n        const payload = msg.payload;\n\n        // Only set link once, and only if edgeConfig exists\n        if (!linkSet && payload.edgeConfig && payload.edgeConfig.edgeId) {\n            const edgeId = payload.edgeConfig.edgeId;\n            const baseUrl = \"https://txc.thmanyah-production-cloud.com/txedges/\";\n\n            const linkEl = document.getElementById('edge-config-link');\n            if (linkEl) {\n                linkEl.href = baseUrl + edgeId;\n                linkSet = true;\n            }\n        }\n\n        if (msg.topic === 'success' && payload.success) {\n            setLoadingState(false);\n            updateLastUpdated();\n            // Removed triggerRefresh to prevent looping\n            return;\n        }\n\n        if (msg.topic === 'error' && payload.success === false) {\n            setLoadingState(false);\n            return;\n        }\n\n        if (payload.edgeConfig) {\n            currentConfig = payload.edgeConfig;\n            edgeName.textContent = currentConfig.edgeName || 'Source Switch Control';\n        }\n\n        if (payload.configuredStreams && payload.configuredStreams.length > 0) {\n            activeStream.textContent = payload.configuredStreams[0].name;\n        }\n\n        if (payload.configuredOutputs && payload.configuredOutputs.length > 0) {\n            outputStream.textContent = payload.configuredOutputs[0].name;\n        }\n\n        if (payload.configuredSources) {\n            populateSourceDropdown(payload.configuredSources);\n        }\n\n        if (typeof payload.online === 'boolean') {\n            onlineIndicator.className = `online-indicator ${payload.online ? 'online' : 'offline'}`;\n        }\n\n        if (msg.currentSource) {\n            updateActiveSource(msg.currentSource);\n        }\n\n        updateLastUpdated();\n        currentState = payload;\n    });\n\n})(scope);\n</script>", "storeOutMessages": true, "fwdInMessages": true, "resendOnRefresh": false, "templateScope": "local", "className": "", "x": 745, "y": 760, "wires": [["cfa84292d3b4a231"]], "l": false}, {"id": "cfa84292d3b4a231", "type": "function", "z": "2ad5052cfb12f2ae", "g": "b93f0ca3c2683877", "name": "Handle UI Input", "func": "// ONLY process explicit user switch requests with proper validation\nif (msg.action === 'switch' && msg.currentSource && msg.switchType === 'preselected') {\n    // Additional validation: must have timestamp and be recent (within last 5 seconds)\n    if (!msg.timestamp) {\n        node.log('[UI INPUT] Rejected: No timestamp in switch request');\n        return [null];\n    }\n\n    const msgTime = new Date(msg.timestamp).getTime();\n    const now = Date.now();\n    const timeDiff = now - msgTime;\n\n    if (timeDiff > 5000) { // More than 5 seconds old\n        node.log(`[UI INPUT] Rejected: Switch request too old (${timeDiff}ms)`);\n        return [null];\n    }\n\n    // Enhanced duplicate prevention with unique request ID\n    const requestId = `${msg.currentSource}_${msgTime}`;\n    const lastRequestId = flow.get('lastSwitchRequestId') || '';\n    \n    if (requestId === lastRequestId) {\n        node.log('[UI INPUT] Rejected: Duplicate switch request detected');\n        return [null];\n    }\n\n    // Check for rapid-fire requests (prevent multiple clicks)\n    const lastSwitchTime = flow.get('lastSwitchRequestTime') || 0;\n    if (now - lastSwitchTime < 2000) { // Increased to 2 seconds for better protection\n        node.log('[UI INPUT] Rejected: Switch request too soon after previous request');\n        return [null];\n    }\n\n    // Store the request details to prevent duplicates\n    flow.set('lastSwitchRequestTime', now);\n    flow.set('lastSwitchRequestId', requestId);\n\n    // User clicked the switch button - proceed with validation\n    const config = flow.get('edgeConfig');\n    if (!config) {\n        node.error('No edge configuration found');\n        return [null];\n    }\n\n    // Find the target source\n    let targetSource = null;\n    if (msg.currentSource === 'main') {\n        targetSource = config.sources.find(s => s.type === 'Main');\n    } else if (msg.currentSource === 'backup') {\n        targetSource = config.sources.find(s => s.type === 'Backup');\n    } else {\n        targetSource = config.sources.find(s => s.type.toLowerCase() === msg.currentSource.toLowerCase());\n    }\n\n    if (!targetSource) {\n        node.error(`No source found for type: ${msg.currentSource}`);\n        return [null];\n    }\n\n    node.log(`[UI INPUT] Processing valid switch request to: ${msg.currentSource}`);\n\n    // Prepare message for switch handler\n    const switchMsg = {\n        payload: msg.currentSource,\n        change: `User requested switch to ${msg.currentSource}`,\n        techex: {\n            baseUrlProd: global.get('SECRETS.TECHEX_TXCORE_URL_PROD'),\n            apiKeyProd: global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD'),\n            edgeId: config.edgeId\n        },\n        requestId: requestId // Include request ID for tracking\n    };\n\n    node.log('[UI INPUT] Triggering switch API');\n\n    // Output only to the switch API flow\n    return [switchMsg];\n}\n\n// For all other message types, reject them\nnode.log(`[UI INPUT] Rejected: Invalid message type - action=${msg.action}, trigger=${msg.trigger || 'none'}`);\nreturn [null];\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 815, "y": 760, "wires": [["c0d3b48d6d9a2436"]], "l": false}, {"id": "1eb86c025d873fec", "type": "link in", "z": "2ad5052cfb12f2ae", "g": "b93f0ca3c2683877", "name": "link in 19", "links": ["8e9b9e3ae9b7062d"], "x": 745, "y": 820, "wires": [["be86550136a3cadc"]]}, {"id": "e722ce8d804812b0", "type": "function", "z": "2ad5052cfb12f2ae", "g": "fbc22c4046eb250b", "name": "Map → Dynamic State Model", "func": "// Get configuration for mapping\nconst config = flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found for mapping');\n    return null;\n}\n\nconst j = msg.payload || {};\n\n// Safely unwrap the envelope we created after each HTTP request\nfunction unwrap(x) {\n    if (x && typeof x === 'object' && 'data' in x) return x.data || {};\n    return x || {};\n}\n\nfunction nameOf(o, fallback) {\n    if (!o || typeof o !== 'object') return fallback;\n    return o.name || o.displayName || o.label || fallback;\n}\n\nfunction isActive(o) {\n    if (!o || typeof o !== 'object') return false;\n    if (typeof o.active === 'boolean') return o.active;\n    if (typeof o.isActive === 'boolean') return o.isActive;\n    if (typeof o.online === 'boolean') return o.online;\n    if (typeof o.status === 'string') return /up|on|online|active/i.test(o.status);\n    return false;\n}\n\n// Unwrap all responses\nconst stream = unwrap(j.stream);\nconst sources = [];\nconst outputs = [];\n\n// Process sources dynamically\nconfig.sources.forEach((sourceConfig, idx) => {\n    const sourceData = unwrap(j[`source${idx}`]);\n    sources.push({\n        name: nameOf(sourceData, sourceConfig.sourceName || `Source ${idx + 1}`),\n        active: isActive(sourceData),\n        type: sourceConfig.type,\n        sourceId: sourceConfig.sourceId\n    });\n});\n\n// Process outputs\nconfig.outputs.forEach((outputConfig, idx) => {\n    const outputData = unwrap(j.output);\n    outputs.push({\n        name: nameOf(outputData, outputConfig.outputName || `Output ${idx + 1}`)\n    });\n});\n\n// Build dynamic model\nconst model = {\n    configuredStreams: [{ name: nameOf(stream, config.streams[0]?.streamName || \"--\") }],\n    configuredSources: sources,\n    configuredOutputs: outputs,\n    online: [stream, ...sources.map(s => ({ active: s.active }))].some(x => isActive(x) || x.active),\n    edgeConfig: config\n};\n\nmsg.payload = model;\n\n// Determine current active source for UI\nconst activeSource = sources.find(s => s.active);\nif (activeSource) {\n    if (activeSource.type === 'Main') {\n        msg.currentSource = 'main';\n    } else if (activeSource.type === 'Backup') {\n        msg.currentSource = 'backup';\n    } else {\n        msg.currentSource = activeSource.type.toLowerCase();\n    }\n} else {\n    msg.currentSource = null;\n}\n\nnode.status({ fill: \"green\", shape: \"dot\", text: \"mapped → dynamic ui model\" });\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 705, "y": 600, "wires": [["a0d7a732a3594f97"]], "l": false}, {"id": "a0d7a732a3594f97", "type": "function", "z": "2ad5052cfb12f2ae", "g": "fbc22c4046eb250b", "name": "Detect Source Change", "func": "// Get the current source from the incoming message\nconst newSource = msg.currentSource;\n\n// Get the previous source state from flow context\nlet oldSource = flow.get('current_source_state');\nif (oldSource === undefined) {\n    oldSource = null;\n}\n\n// Always update the stored state to current\nflow.set('current_source_state', newSource);\n\n// Preserve the complete payload for UI display\n// Add change information without overwriting the payload\nif (newSource !== oldSource) {\n    msg.change = `Source changed from ${oldSource || 'initial'} to ${newSource}`;\n    msg.changeDetected = true;\n    node.log(`[INFO] Source state updated: ${oldSource || 'initial'} → ${newSource}`);\n} else {\n    msg.change = `Source state confirmed: ${newSource}`;\n    msg.changeDetected = false;\n}\n\n// Add status details to payload for UI display\nif (msg.payload && typeof msg.payload === 'object') {\n    msg.payload.lastChange = msg.change;\n    msg.payload.changeTimestamp = new Date().toISOString();\n    msg.payload.previousSource = oldSource;\n    msg.payload.currentActiveSource = newSource;\n}\n\n// ALWAYS send complete data to UI, but NO automatic switching\n// This eliminates the infinite loop while preserving UI updates\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 845, "y": 600, "wires": [["be86550136a3cadc"]], "l": false}, {"id": "d8dbc957d90c14b5", "type": "function", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "HTTP → <PERSON><PERSON>", "func": "// Wrap HTTP response and split OK vs Error to 2 outputs\n// Output 1: normalized wrapper { ok, statusCode, data } → Join\n// Output 2: only when HTTP error (statusCode >= 400 or msg.error)\n\nconst sc = Number(msg.statusCode) || 0;\n\n// Preserve topic/parts for Join; do not touch them\nconst wrapped = {\n    ok: sc >= 200 && sc < 300,\n    statusCode: sc,\n    data: msg.payload\n};\n\nmsg.payload = wrapped;\n\n// Decide if this is an error\nconst isHttpError = !wrapped.ok || !!msg.error || sc === 0;\n\n// Optional status indicator in editor\nnode.status({\n    fill: wrapped.ok ? \"green\" : \"red\",\n    shape: wrapped.ok ? \"dot\" : \"ring\",\n    text: `HTTP ${sc || \"?\"}`\n});\n\nif (isHttpError) {\n    // Build a lightweight error copy preserving routing fields\n    const errMsg = {\n        _isHttpError: true,\n        topic: msg.topic,\n        parts: msg.parts,\n        statusCode: sc,\n        error: msg.error || null,\n        url: msg.url,\n        method: msg.method\n    };\n    return [msg, errMsg];\n}\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 145, "y": 620, "wires": [["c1b883b40cd16596"], ["a83bf48993197dad"]], "l": false}, {"id": "c1b883b40cd16596", "type": "join", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "Dynamic Join (by topic, per groupId)", "mode": "manual", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": true, "accumulate": false, "timeout": "10", "count": "", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 235, "y": 620, "wires": [["aaeb4c4abec81b4b"]], "l": false}, {"id": "aaeb4c4abec81b4b", "type": "function", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "Gate & Retry on 429", "func": "// Get expected configuration to validate completeness\nconst config = flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found for validation');\n    return [null, msg];\n}\n\n// Expected topics based on configuration\nconst expectedTopics = [];\nconfig.streams.forEach(() => expectedTopics.push('stream'));\nconfig.sources.forEach((_, idx) => expectedTopics.push(`source${idx}`));\nconfig.outputs.forEach(() => expectedTopics.push('output'));\n\nconst p = msg.payload || {};\nconst receivedTopics = Object.keys(p);\n\n// Check if we have all expected topics\nconst isComplete = expectedTopics.every(topic => receivedTopics.includes(topic));\n\nif (!isComplete) {\n    node.status({fill:\"grey\", shape:\"ring\", text:\"incomplete join → retry\"});\n    return [null, msg];\n}\n\n// Check for rate limiting (429 errors)\nconst values = Object.values(p);\nconst any429 = values.some(v => v && v.statusCode === 429);\nconst anyHardErr = values.some(v => v && v.ok === false && v.statusCode >= 400);\n\nif (any429) {\n    node.status({fill:\"yellow\", shape:\"ring\", text:\"rate limited (429) – retry 10s\"});\n    msg.topic = \"warning\";\n    msg._reason = \"429\";\n    return [null, msg];\n}\n\nif (anyHardErr) {\n    node.status({fill:\"red\", shape:\"dot\", text:\"one or more API errors\"});\n    // Still pass through so UI can show last known state\n    return [msg, null];\n}\n\nnode.status({fill:\"green\", shape:\"dot\", text:\"OK\"});\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 315, "y": 620, "wires": [["e722ce8d804812b0"], ["559b3c8ba7e65d63"]], "l": false}, {"id": "559b3c8ba7e65d63", "type": "change", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "Reset Group & Clean for Retry", "rules": [{"t": "delete", "p": "parts", "pt": "msg"}, {"t": "delete", "p": "topic", "pt": "msg"}, {"t": "delete", "p": "payload", "pt": "msg"}, {"t": "set", "p": "_retry", "pt": "msg", "to": "429", "tot": "str"}], "x": 235, "y": 720, "wires": [["c7f87e99c8c3662c"]], "l": false}, {"id": "c7f87e99c8c3662c", "type": "delay", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "Retry after 10s", "pauseType": "delay", "timeout": "10", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 360, "y": 720, "wires": [["3a2dace9cca58a66"]]}, {"id": "3a2dace9cca58a66", "type": "change", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "Clean All for Retry", "rules": [{"t": "delete", "p": "_msgid", "pt": "msg"}, {"t": "delete", "p": "_groupId", "pt": "msg"}, {"t": "delete", "p": "techex", "pt": "msg"}, {"t": "delete", "p": "_retry", "pt": "msg"}], "x": 485, "y": 720, "wires": [["df19abe2768339d0"]], "l": false}, {"id": "a83bf48993197dad", "type": "debug", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "Debug: HTT<PERSON> Error", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 145, "y": 680, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "df19abe2768339d0", "type": "link out", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "link out 15", "mode": "link", "links": ["e4484e4f9a5621e0"], "x": 485, "y": 780, "wires": []}, {"id": "40789a6216d5beb5", "type": "link in", "z": "2ad5052cfb12f2ae", "g": "02851485590b35b6", "name": "link in 20", "links": ["e152a5207e38aaea"], "x": 85, "y": 580, "wires": [["d8dbc957d90c14b5"]]}, {"id": "675b74351fa25e40", "type": "function", "z": "2ad5052cfb12f2ae", "g": "94f9dcc4badd6e17", "name": "Dynamic API Processor", "func": "// Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token = msg.techex?.apiKeyProd;\nconst edgeId = msg.techex?.edgeId;\n\n// Validate required configuration\nif (!baseUrl || !token || !edgeId) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd, apiKeyProd, or edgeId\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token, hasEdgeId: !!edgeId }\n    }];\n}\n\n// Determine endpoint based on topic\nlet endpoint = '';\nlet resourceId = '';\n\nif (msg.topic === 'stream') {\n    resourceId = msg.techex.streamId;\n    if (!resourceId) {\n        return [null, { error: \"Missing streamId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/stream/${encodeURIComponent(resourceId)}`;\n} else if (msg.topic.startsWith('source')) {\n    resourceId = msg.techex.sourceId;\n    if (!resourceId) {\n        return [null, { error: \"Missing sourceId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/source/${encodeURIComponent(resourceId)}`;\n} else if (msg.topic === 'output') {\n    resourceId = msg.techex.outputId;\n    if (!resourceId) {\n        return [null, { error: \"Missing outputId\", statusCode: 400 }];\n    }\n    endpoint = `/api/mwedge/${encodeURIComponent(edgeId)}/output/${encodeURIComponent(resourceId)}`;\n} else {\n    return [null, { error: `Unknown topic: ${msg.topic}`, statusCode: 400 }];\n}\n\n// Rate limiting (10ms interval)\nconst operationName = `api_${msg.topic}`;\nconst rlKey = `rateLimit.${operationName}`;\nconst last = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 10; // ms\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        details: {},\n        retryAfter\n    }];\n}\nflow.set(rlKey, Date.now());\n\n// Build request\ntry {\n    msg.url = `${baseUrl.replace(/\\/$/, '')}${endpoint}`;\n    msg.method = 'GET';\n    \n    // Set headers\n    msg.headers = {\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n    \n    // Visual status in editor\n    node.status({ fill: 'green', shape: 'dot', text: `${msg.method} ${msg.topic}` });\n    \n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1040, "y": 280, "wires": [["cfd9385a6ae54342"], ["c3d1ac8be15bda1d"]]}, {"id": "cfd9385a6ae54342", "type": "http request", "z": "2ad5052cfb12f2ae", "g": "94f9dcc4badd6e17", "name": "API Request", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1205, "y": 280, "wires": [["b5d20460914acbb2"]], "l": false}, {"id": "b5d20460914acbb2", "type": "change", "z": "2ad5052cfb12f2ae", "g": "94f9dcc4badd6e17", "name": "Clean Headers", "rules": [{"t": "delete", "p": "method", "pt": "msg"}, {"t": "delete", "p": "headers", "pt": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 1275, "y": 280, "wires": [["e152a5207e38aaea"]], "l": false}, {"id": "c3d1ac8be15bda1d", "type": "debug", "z": "2ad5052cfb12f2ae", "g": "94f9dcc4badd6e17", "name": "Debug: API Setup Error", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1115, "y": 340, "wires": [], "icon": "node-red/alert.svg", "l": false}, {"id": "e152a5207e38aaea", "type": "link out", "z": "2ad5052cfb12f2ae", "g": "94f9dcc4badd6e17", "name": "link out 16", "mode": "link", "links": ["40789a6216d5beb5"], "x": 1275, "y": 340, "wires": []}, {"id": "cd16e84b5eb0e803", "type": "function", "z": "2ad5052cfb12f2ae", "g": "da0748b76365bcee", "name": "Dynamic API Orchestrator", "func": "// Get configuration from flow context or message\nconst config = msg.edgeConfig || flow.get('edgeConfig');\nif (!config) {\n    node.error('No edge configuration found');\n    return null;\n}\n\n// Generate unique group ID for this batch\nconst groupId = msg._groupId || Date.now().toString();\n\n// Calculate total parts count\nconst totalParts = config.streams.length + config.sources.length + config.outputs.length;\n\n// Base message template\nconst baseMsg = {\n    _groupId: groupId,\n    techex: {\n        baseUrlProd: global.get('SECRETS.TECHEX_TXCORE_URL_PROD'),\n        apiKeyProd: global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD'),\n        edgeId: config.edgeId\n    }\n};\n\nconst messages = [];\nlet partIndex = 0;\n\n// Generate stream requests\nconfig.streams.forEach((stream, streamIdx) => {\n    const streamMsg = JSON.parse(JSON.stringify(baseMsg));\n    streamMsg.techex.streamId = stream.streamId;\n    streamMsg.topic = 'stream';\n    streamMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    streamMsg._delay = 0; // No delay for streams\n    messages.push(streamMsg);\n});\n\n// Generate source requests with staggered delays\nconfig.sources.forEach((source, sourceIdx) => {\n    const sourceMsg = JSON.parse(JSON.stringify(baseMsg));\n    sourceMsg.techex.sourceId = source.sourceId;\n    sourceMsg.topic = `source${sourceIdx}`;\n    sourceMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    sourceMsg._delay = (sourceIdx + 1) * 1000; // 1s, 2s delays\n    messages.push(sourceMsg);\n});\n\n// Generate output requests\nconfig.outputs.forEach((output, outputIdx) => {\n    const outputMsg = JSON.parse(JSON.stringify(baseMsg));\n    outputMsg.techex.outputId = output.outputId;\n    outputMsg.topic = 'output';\n    outputMsg.parts = {\n        id: groupId,\n        index: partIndex++,\n        count: totalParts\n    };\n    outputMsg._delay = (config.sources.length + 1) * 1000; // After all sources\n    messages.push(outputMsg);\n});\n\nreturn [messages];", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 475, "y": 240, "wires": [["32f7cd8b9b5638c3"]], "l": false}, {"id": "32f7cd8b9b5638c3", "type": "function", "z": "2ad5052cfb12f2ae", "g": "da0748b76365bcee", "name": "Delay Router", "func": "// Route messages based on delay requirements\nconst delay = msg._delay || 0;\n\nif (delay === 0) {\n    // No delay - send immediately\n    return [msg, null, null, null];\n} else if (delay === 1000) {\n    // 1 second delay\n    return [null, msg, null, null];\n} else if (delay === 2000) {\n    // 2 second delay\n    return [null, null, msg, null];\n} else {\n    // 3+ second delay\n    return [null, null, null, msg];\n}", "outputs": 4, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 600, "y": 240, "wires": [["675b74351fa25e40"], ["7b1769775f8f615a"], ["c2f63a97f5008000"], ["49f88af475683595"]]}, {"id": "7b1769775f8f615a", "type": "delay", "z": "2ad5052cfb12f2ae", "g": "da0748b76365bcee", "name": "1s Delay", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 780, "y": 280, "wires": [["675b74351fa25e40"]]}, {"id": "c2f63a97f5008000", "type": "delay", "z": "2ad5052cfb12f2ae", "g": "da0748b76365bcee", "name": "2s <PERSON><PERSON>", "pauseType": "delay", "timeout": "2", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 780, "y": 340, "wires": [["675b74351fa25e40"]]}, {"id": "49f88af475683595", "type": "delay", "z": "2ad5052cfb12f2ae", "g": "da0748b76365bcee", "name": "3s <PERSON><PERSON>", "pauseType": "delay", "timeout": "3", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 780, "y": 400, "wires": [["675b74351fa25e40"]]}, {"id": "aa5ffe42cc414351", "type": "function", "z": "2ad5052cfb12f2ae", "g": "6e6b6d145d9c6d01", "name": "Configuration Store", "func": "// Dynamic Edge Configuration\nconst edgeConfig = {\n    \"edgeId\": \"6884e7d6539d77930a455419\",\n    \"edgeName\": \"MATCH-1-CAM1ISO-eu-cont-az2\",\n    \"sources\": [\n        {\n            \"sourceId\": \"19fcb73e09add13df4f028b6bb24d6ef70ec4a9bfd4e58dc\",\n            \"sourceName\": \"MATCH-1-CAM1ISO-SPL-eu-cont-az2-Input A\",\n            \"type\": \"Main\"\n        },\n        {\n            \"sourceId\": \"28c451bfc835a47477c53e621665cedea3d0a9ad52830dc5\",\n            \"sourceName\": \"MATCH-1-CAM1ISO-eu-cont-az2-Input Caller\",\n            \"type\": \"Backup\"\n        }\n    ],\n    \"outputs\": [\n        {\n            \"outputId\": \"6a43bceb2fb2e8d4b56ea53afdec19f25ae2253e527c3a8f\",\n            \"outputName\": \"MATCH-1-CAM1ISO-SPL-eu-cont-az2-Output\"\n        }\n    ],\n    \"streams\": [\n        {\n            \"streamId\": \"aafc18ca99d0016f68e074c2326bcc11c4ff2bf411f6a459\",\n            \"streamName\": \"MATCH-1-CAM1ISO-SPL-eu-cont-az2\"\n        }\n    ]\n}\n\n// Store configuration in flow context\nflow.set('edgeConfig', edgeConfig);\n\nmsg.edgeConfig = edgeConfig;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 290, "y": 220, "wires": [["cd16e84b5eb0e803"]]}, {"id": "b40fb08a4133735f", "type": "inject", "z": "2ad5052cfb12f2ae", "g": "6e6b6d145d9c6d01", "name": "10s Sync Timer", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "10", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "sync", "payload": "", "payloadType": "date", "x": 125, "y": 220, "wires": [["aa5ffe42cc414351"]], "icon": "node-red-contrib-loop-processing/loop.png", "l": false}, {"id": "e4484e4f9a5621e0", "type": "link in", "z": "2ad5052cfb12f2ae", "g": "6e6b6d145d9c6d01", "name": "link in 21", "links": ["df19abe2768339d0", "8e9b9e3ae9b7062d"], "x": 165, "y": 280, "wires": [["aa5ffe42cc414351"]]}, {"id": "abdca6d8da3c3f58", "type": "ui_group", "name": "Source Switch Group - 6", "tab": "c5f3abebddfeef18", "order": 1, "disp": false, "width": "7", "collapse": false, "className": ""}, {"id": "c5f3abebddfeef18", "type": "ui_tab", "name": "Tab 7", "icon": "dashboard", "order": 7}]